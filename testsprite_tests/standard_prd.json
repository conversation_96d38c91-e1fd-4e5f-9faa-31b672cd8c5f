{"meta": {"project": "tagTok - TikTok Video Organization Platform", "date": "2025-08-25", "prepared_by": "Software Development Manager"}, "product_overview": "tagTok is a comprehensive full-stack web application designed for uploading, transcribing, organizing, and analyzing TikTok videos. Leveraging AI technologies, it offers smart tagging, advanced search, and detailed video analytics with a seamless, responsive interface optimized for mobile and desktop use.", "core_goals": ["Enable users to upload and manage multiple TikTok videos with ease via a drag-and-drop interface.", "Automatically generate accurate transcriptions and AI-powered tags to enhance video organization and searchability.", "Provide advanced search and filtering options on video metadata, transcript content, and tags.", "Deliver rich data visualization and analytics on video usage, tags, languages, and upload trends.", "Ensure a performant and reliable local deployment without cloud dependencies, supporting both local network and remote server use cases.", "Offer bulk operations such as tagging and batch actions to efficiently manage large video libraries.", "Provide export capabilities for videos, tags, and analytics data in various formats.", "Maintain a user-friendly, mobile-responsive UI with persistent preferences including theme management."], "key_features": ["Multi-video upload with drag-and-drop and progress tracking support.", "AI-powered transcription using OpenAI Whisper integrating CPU-only compatibility.", "Smart tagging system that auto-generates tags via spaCy and KeyBERT with color coding and batch operations.", "Advanced search functionality filtering by tags, transcript content, date, and metadata.", "Built-in video player with thumbnail generation, metadata editing, and transcript viewing.", "Comprehensive analytics dashboard covering upload trends, tag popularity, language distribution, and video duration statistics.", "Download management allowing video downloads with support for TikTok videos including progress indicators.", "Bulk video operations enabling batch tagging, editing, and deletion.", "Data export in CSV and JSON formats for videos, tags, and analytics.", "Real-time processing status indicators for transcription, tagging, and analysis tasks.", "Dark/light theme toggling with preference persistence.", "Robust backend APIs with FastAPI and SQLite enhanced by frontend React TypeScript integration.", "Deployment-ready Dockerized architecture with nginx reverse proxy and detailed deployment guides."], "user_flow_summary": ["Users start by accessing the Upload page where they drag and drop TikTok videos for upload.", "Videos are processed asynchronously on the backend with AI transcription and tagging triggered automatically.", "Processed videos appear in the gallery view with thumbnail, metadata, and tag information.", "User can play videos using the integrated player and view or edit transcripts and metadata.", "Users navigate to the Tags manager to create, edit, and assign colored tags individually or in bulk to videos.", "Advanced search and filter options allow users to find videos by tags, transcript text, upload date, or language.", "Analytics dashboard provides visual insights through charts showing trends such as upload timelines, tag popularity, and language distribution.", "Bulk actions toolbar allows users to select multiple videos for tagging, downloading, or deletion.", "Users can export datasets for videos, tags, or analytics from the export menu.", "Theme toggle enables switching between dark and light modes across the application.", "Administrators or advanced users deploy the application locally or remotely using Docker Compose, following the detailed documentation."], "validation_criteria": ["Successful upload and processing of multiple TikTok videos with visible progress indicators.", "Accurate AI-generated transcriptions and relevant smart tags appearing after processing.", "Responsive and correct filtering results when searching by tags, transcript content, or metadata.", "Functional video player with support for playback controls, thumbnail previews, and transcript display.", "Robust analytics dashboard rendering charts and statistics consistent with video library data.", "Bulk operations affecting multiple videos complete without errors and reflect changes instantly.", "Exports generate correct CSV/JSON files containing requested video, tag, or analytics data.", "Real-time processing status indicators update accurately reflecting backend task states.", "Theme toggling persists user preferences across sessions without UI glitches.", "Application accessible and responsive on desktop, tablet, and mobile devices.", "Backend and frontend APIs respond within acceptable timeframes and handle errors gracefully.", "Full application successfully deploys via the provided Docker Compose configurations, passing health checks on all services."], "code_summary": {"tech_stack": ["React", "TypeScript", "Vite", "Tailwind CSS", "TanStack Query", "TanStack Table", "FastAPI", "Python", "SQLite", "Ollama", "<PERSON>er", "<PERSON><PERSON><PERSON>", "React Router", "Video.js", "Framer Motion"], "features": [{"name": "Video Management", "description": "Core video upload, viewing, and organization functionality with thumbnail generation and metadata management", "files": ["frontend/src/pages/HomePage.tsx", "frontend/src/pages/VideoDetailPage.tsx", "frontend/src/components/VideoCard.tsx", "frontend/src/components/VideoGrid.tsx", "frontend/src/components/VideoPlayer.tsx", "backend/routers/videos.py", "backend/services/video_service.py"]}, {"name": "Tag Management", "description": "Comprehensive tag system for organizing videos with color coding, bulk operations, and filtering", "files": ["frontend/src/components/TagManager.tsx", "frontend/src/components/TagColorPicker.tsx", "frontend/src/components/TagCloud.tsx", "frontend/src/components/TagChart.tsx", "frontend/src/components/BulkTagModal.tsx", "backend/routers/tags.py", "backend/services/tag_service.py"]}, {"name": "Analytics Dashboard", "description": "Data visualization and analytics with charts for video statistics, tag distribution, and upload trends", "files": ["frontend/src/pages/AnalyticsPage.tsx", "frontend/src/components/StatsCard.tsx", "frontend/src/components/DurationDistributionChart.tsx", "frontend/src/components/LanguageDistributionChart.tsx", "frontend/src/components/UploadTimelineChart.tsx", "backend/routers/analytics.py", "backend/services/analytics_service.py"]}, {"name": "Recipe Extraction", "description": "AI-powered recipe detection and extraction from video content using LLM processing", "files": ["frontend/src/pages/RecipesPage.tsx", "frontend/src/components/RecipeCard.tsx", "backend/routers/recipes.py", "backend/services/recipe_service.py", "backend/utils/recipe_extractor.py", "backend/utils/extract_recipes_batch.py"]}, {"name": "Download Management", "description": "TikTok video download functionality with progress tracking and URL processing", "files": ["frontend/src/pages/DownloadPage.tsx", "frontend/src/components/DownloadForm.tsx", "frontend/src/components/DownloadProgress.tsx", "backend/services/download_service.py"]}, {"name": "Search and Filtering", "description": "Advanced search functionality with filters for tags, dates, and video metadata", "files": ["frontend/src/components/SearchBar.tsx", "frontend/src/components/FilterPanel.tsx", "frontend/src/hooks/useDownloadStatus.ts", "frontend/src/hooks/useProcessingStatus.ts"]}, {"name": "Bulk Operations", "description": "Batch processing capabilities for multiple videos including bulk tagging and actions", "files": ["frontend/src/components/BulkActionsToolbar.tsx", "frontend/src/components/BulkTagModal.tsx", "frontend/src/contexts/VideoSelectionContext.tsx"]}, {"name": "Export Functionality", "description": "Data export capabilities for videos, tags, and analytics in various formats", "files": ["backend/routers/export.py", "backend/services/export_service.py"]}, {"name": "Processing Status", "description": "Real-time status tracking for video processing, AI analysis, and background tasks", "files": ["frontend/src/components/ProcessingStatusIndicator.tsx", "frontend/src/components/GlobalProcessingIndicator.tsx", "backend/services/processing_service.py"]}, {"name": "Theme Management", "description": "Dark/light theme switching with persistent user preferences", "files": ["frontend/src/components/ThemeToggle.tsx", "frontend/src/contexts/ThemeContext.tsx"]}, {"name": "File Upload", "description": "Drag-and-drop file upload interface with progress tracking and validation", "files": ["frontend/src/pages/UploadPage.tsx", "frontend/src/components/FileDropzone.tsx"]}, {"name": "Transcript Viewer", "description": "AI-generated transcript display and editing for video content", "files": ["frontend/src/components/TranscriptViewer.tsx"]}, {"name": "Layout and Navigation", "description": "Main application layout with navigation, error handling, and loading states", "files": ["frontend/src/components/Layout.tsx", "frontend/src/components/LoadingSpinner.tsx", "frontend/src/components/ErrorMessage.tsx", "frontend/src/components/ServerOverloadMessage.tsx", "frontend/src/App.tsx"]}, {"name": "API Integration", "description": "Frontend-backend communication with error handling and circuit breaker patterns", "files": ["frontend/src/utils/api.ts", "frontend/src/utils/circuitBreaker.ts"]}]}}