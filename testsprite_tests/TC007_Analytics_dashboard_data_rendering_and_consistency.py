import asyncio
from playwright import async_api

async def run_test():
    pw = None
    browser = None
    context = None
    
    try:
        # Start a Playwright session in asynchronous mode
        pw = await async_api.async_playwright().start()
        
        # Launch a Chromium browser in headless mode with custom arguments
        browser = await pw.chromium.launch(
            headless=True,
            args=[
                "--window-size=1280,720",         # Set the browser window size
                "--disable-dev-shm-usage",        # Avoid using /dev/shm which can cause issues in containers
                "--ipc=host",                     # Use host-level IPC for better stability
                "--single-process"                # Run the browser in a single process mode
            ],
        )
        
        # Create a new browser context (like an incognito window)
        context = await browser.new_context()
        context.set_default_timeout(5000)
        
        # Open a new page in the browser context
        page = await context.new_page()
        
        # Navigate to your target URL and wait until the network request is committed
        await page.goto("http://localhost:3001", wait_until="commit", timeout=10000)
        
        # Wait for the main page to reach DOMContentLoaded state (optional for stability)
        try:
            await page.wait_for_load_state("domcontentloaded", timeout=3000)
        except async_api.Error:
            pass
        
        # Iterate through all iframes and wait for them to load as well
        for frame in page.frames:
            try:
                await frame.wait_for_load_state("domcontentloaded", timeout=3000)
            except async_api.Error:
                pass
        
        # Interact with the page elements to simulate user flow
        # Navigate to the Analytics Dashboard by clicking the Analytics link.
        frame = context.pages[-1]
        elem = frame.locator('xpath=html/body/div/div/div/div[2]/div/div[2]/nav/a[5]').nth(0)
        await page.wait_for_timeout(3000); await elem.click(timeout=5000)
        

        # Assert that the upload timeline chart shows no data available message
        upload_timeline_text = await frame.locator('text=No upload data available').text_content()
        assert upload_timeline_text == 'No upload data available', 'Upload timeline chart should indicate no data available when no uploads exist'
          
        # Assert that the tag popularity and distribution charts show no tag data available
        top_tags_text = await frame.locator('text=No tag data available').text_content()
        assert top_tags_text == 'No tag data available', 'Tag popularity chart should indicate no tag data available when no tags exist'
          
        # Assert that the language distribution chart shows no language data available
        language_distribution_text = await frame.locator('text=No language data available').text_content()
        assert language_distribution_text == 'No language data available', 'Language distribution chart should indicate no language data available when no language data exists'
          
        # Assert that video duration distribution statistics are consistent with zero videos
        video_duration_stats = await frame.locator('xpath=//div[contains(text(), "0-30s")]').all_text_contents()
        assert any('0%' in text for text in video_duration_stats), 'Video duration distribution should show 0% for all categories when no videos exist'
          
        # Assert quick statistics values for zero videos
        total_videos_text = await frame.locator('text=Total Videos: 0').text_content()
        assert '0' in total_videos_text, 'Total Videos should be 0'
        tags_per_video_text = await frame.locator('text=Tags per Video: 0').text_content()
        assert '0' in tags_per_video_text, 'Tags per Video should be 0'
        most_popular_tag_text = await frame.locator('text=Most Popular Tag: None').text_content()
        assert 'None' in most_popular_tag_text, 'Most Popular Tag should be None'
        await asyncio.sleep(5)
    
    finally:
        if context:
            await context.close()
        if browser:
            await browser.close()
        if pw:
            await pw.stop()
            
asyncio.run(run_test())
    