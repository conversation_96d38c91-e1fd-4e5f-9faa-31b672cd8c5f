[{"id": "TC001", "title": "Multi-video upload with drag-and-drop", "description": "Verify successful upload of multiple TikTok videos via drag-and-drop interface with progress tracking.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the Upload page."}, {"type": "action", "description": "Drag and drop multiple valid TikTok video files into the upload area."}, {"type": "assertion", "description": "Confirm upload progress indicators display accurately for each video."}, {"type": "assertion", "description": "Verify all videos are successfully uploaded and listed in the gallery."}]}, {"id": "TC002", "title": "Upload of invalid video formats", "description": "Verify system handles invalid or unsupported video file formats gracefully during upload.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Navigate to Upload page."}, {"type": "action", "description": "Attempt to drag and drop unsupported or corrupted files."}, {"type": "assertion", "description": "Check that the system rejects unsupported formats with clear error messages."}, {"type": "assertion", "description": "Ensure no invalid files are added to the video gallery."}]}, {"id": "TC003", "title": "Automatic AI transcription and tagging", "description": "Validate that after video upload, AI transcription and smart tagging occur automatically and results appear correctly.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Upload a valid TikTok video."}, {"type": "assertion", "description": "Confirm real-time processing status indicator shows transcription and tagging progress."}, {"type": "assertion", "description": "Verify that accurate transcription text is generated and associated with the video."}, {"type": "assertion", "description": "Validate that relevant AI-generated tags appear with color coding and batch operation support."}]}, {"id": "TC004", "title": "Playback and transcript display and editing", "description": "Ensure integrated video player supports playing videos, thumbnail previews, and transcript viewing and editing.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Select a processed video in the gallery."}, {"type": "assertion", "description": "Confirm video plays smoothly with playback controls available."}, {"type": "assertion", "description": "Check that video thumbnail is displayed correctly."}, {"type": "action", "description": "Open transcript viewer and edit some transcription text."}, {"type": "assertion", "description": "Verify edited transcript is saved and reflected properly."}]}, {"id": "TC005", "title": "Search and filter videos by tags and metadata", "description": "Validate advanced search features filtering videos by tags, transcript content, upload date, and other metadata.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to search interface."}, {"type": "action", "description": "Input search criteria including tag filters, transcript keywords, date ranges."}, {"type": "assertion", "description": "Ensure filter logic returns correct and relevant video results matching criteria."}, {"type": "action", "description": "Clear filters and attempt partial or empty searches."}, {"type": "assertion", "description": "Verify system handles empty and edge search inputs gracefully without errors."}]}, {"id": "TC006", "title": "Bulk tagging operations on multiple videos", "description": "Verify bulk tagging functionality allows batch selection and tag assignment or removal on multiple videos.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Select multiple videos from gallery using bulk selection."}, {"type": "action", "description": "Open bulk tag modal and apply a new or existing tag to all selected videos."}, {"type": "assertion", "description": "Confirm tags are applied correctly across all selected videos immediately after operation."}, {"type": "action", "description": "Remove a tag from all selected videos via bulk operation."}, {"type": "assertion", "description": "Validate removal reflects instantly on UI and backend."}]}, {"id": "TC007", "title": "Analytics dashboard data rendering and consistency", "description": "Check that the analytics dashboard accurately displays charts and statistics consistent with video and tag data.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the Analytics Dashboard."}, {"type": "assertion", "description": "Verify upload timeline chart renders correct trend data."}, {"type": "assertion", "description": "Check tag popularity and distribution charts reflect actual tag usage."}, {"type": "assertion", "description": "Validate language distribution and video duration statistics are consistent with uploaded video properties."}]}, {"id": "TC008", "title": "Download TikTok video with progress tracking", "description": "Validate video download manager supports downloading TikTok videos with real-time progress indicators.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the Download page."}, {"type": "action", "description": "Input a valid TikTok video URL."}, {"type": "action", "description": "Start download."}, {"type": "assertion", "description": "Verify download progress bar updates accurately during download."}, {"type": "assertion", "description": "Ensure video file is downloaded successfully and accessible."}]}, {"id": "TC009", "title": "Bulk video download and deletion operations", "description": "Verify that users can perform batch downloads and deletions on multiple selected videos via bulk actions toolbar.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Select multiple videos in gallery using bulk selection."}, {"type": "action", "description": "Trigger bulk download and ensure all selected videos start downloads."}, {"type": "assertion", "description": "Confirm downloads commence with progress indicated for each video."}, {"type": "action", "description": "Trigger bulk deletion action and confirm deletion prompt."}, {"type": "assertion", "description": "Verify all selected videos are removed from gallery upon confirmation."}]}, {"id": "TC010", "title": "Export data in CSV and JSON formats", "description": "Validate export functionality generates accurate CSV and JSON files for videos, tags, and analytics data.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to export menu."}, {"type": "action", "description": "Select dataset type for export: videos, tags, or analytics."}, {"type": "action", "description": "Export data in CSV format and save file."}, {"type": "assertion", "description": "Verify CSV file contents are correct and properly formatted."}, {"type": "action", "description": "Export the same dataset in JSON format."}, {"type": "assertion", "description": "Confirm JSON file contents reflect accurate data and valid syntax."}]}, {"id": "TC011", "title": "Real-time processing status indicators", "description": "Ensure processing status indicators update correctly to reflect the state of transcription, tagging, and analysis tasks.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Upload a new video to trigger processing."}, {"type": "assertion", "description": "Observe real-time status indicators for transcription and tagging showing correct progress states."}, {"type": "assertion", "description": "Validate status indicators update to completion once processing tasks finish."}]}, {"id": "TC012", "title": "Theme toggling with preference persistence", "description": "Verify that toggling between dark and light themes updates the UI and remembers user preference across sessions.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Toggle theme from light to dark mode."}, {"type": "assertion", "description": "Confirm UI updates correctly with dark theme styling."}, {"type": "action", "description": "Reload the application."}, {"type": "assertion", "description": "Verify dark theme preference persists after reload."}, {"type": "action", "description": "Toggle back to light theme and confirm similar behavior."}]}, {"id": "TC013", "title": "Responsive UI behavior across devices", "description": "Validate application UI is responsive and usable on desktop, tablet, and mobile screen sizes.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Open application on desktop viewport."}, {"type": "assertion", "description": "Verify layout and navigation are correctly displayed and usable."}, {"type": "action", "description": "Simulate tablet viewport and reload."}, {"type": "assertion", "description": "Check UI components adapt layout properly without overlap or truncation."}, {"type": "action", "description": "Simulate mobile viewport with common resolutions."}, {"type": "assertion", "description": "Ensure menus, buttons, drag-and-drop zones, and all interactive elements remain accessible and functional."}]}, {"id": "TC014", "title": "Robust backend API error handling", "description": "Test backend APIs for graceful handling of invalid inputs, server errors, and response timeouts.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Send API requests with malformed data to key endpoints (upload, tagging, search)."}, {"type": "assertion", "description": "Validate backend responds with appropriate error codes and descriptive error messages."}, {"type": "action", "description": "Simulate backend server errors or timeouts and invoke API calls."}, {"type": "assertion", "description": "Ensure frontend handles errors gracefully with user-friendly notifications and retry options."}]}, {"id": "TC015", "title": "Dockerized deployment and health checks", "description": "Confirm the full application stack deploys successfully via Docker Compose and passes all health checks for services.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Follow deployment guide to launch the application using Docker Compose."}, {"type": "assertion", "description": "Verify all containers (backend, frontend, nginx, database) start without errors."}, {"type": "assertion", "description": "Check health check endpoints respond status as healthy."}, {"type": "assertion", "description": "Access application UI and verify core functionality is available after deployment."}]}, {"id": "TC016", "title": "AI-powered recipe detection and extraction", "description": "Validate that AI extracts recipe information correctly from video content and displays it on the Recipes page.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Upload or select a video containing recipe content."}, {"type": "assertion", "description": "Confirm AI-powered recipe extraction service processes the video."}, {"type": "action", "description": "Navigate to Recipes page."}, {"type": "assertion", "description": "Verify extracted recipes appear with correct ingredients and instructions."}]}, {"id": "TC017", "title": "Bulk operations on empty selection", "description": "Validate system behavior when bulk operations are triggered with no videos selected.", "category": "error handling", "priority": "Medium", "steps": [{"type": "action", "description": "Ensure no videos are selected in the gallery."}, {"type": "action", "description": "Attempt to open bulk tag modal or perform bulk delete/download."}, {"type": "assertion", "description": "Verify system prevents operation and shows informative message about no selection."}]}, {"id": "TC018", "title": "Video metadata editing with validation", "description": "Verify metadata editing UI supports updating video info and enforces validation rules.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Open video details page for a selected video."}, {"type": "action", "description": "Edit metadata fields such as title, description, and date."}, {"type": "action", "description": "Attempt to submit invalid or empty required fields."}, {"type": "assertion", "description": "Confirm validation errors are shown and prevent submission."}, {"type": "action", "description": "Correct inputs and save changes."}, {"type": "assertion", "description": "Verify metadata updates persist and display correctly after save."}]}, {"id": "TC019", "title": "Frontend-backend API response time and circuit breaker", "description": "Test API requests for response times within acceptable limits and circuit breaker pattern functioning on failures.", "category": "performance", "priority": "Medium", "steps": [{"type": "action", "description": "Initiate various API calls (upload status, search, tagging) under normal load."}, {"type": "assertion", "description": "Verify response times meet performance criteria."}, {"type": "action", "description": "Simulate backend slowdowns or failures."}, {"type": "assertion", "description": "Confirm frontend triggers circuit breaker and fails fast with proper fallback UI."}]}, {"id": "TC020", "title": "Transcript viewer edge cases", "description": "Validate transcript viewer handles very long transcripts, special characters, and empty transcripts without errors.", "category": "error handling", "priority": "Low", "steps": [{"type": "action", "description": "Open transcript viewer for a video with a very long transcription."}, {"type": "assertion", "description": "Ensure viewer displays full transcript with smooth scrolling and no performance issues."}, {"type": "action", "description": "Open transcript viewer for a video with special or non-ASCII characters."}, {"type": "assertion", "description": "Verify special characters render correctly without UI glitches."}, {"type": "action", "description": "Open transcript viewer for a video with no transcription available."}, {"type": "assertion", "description": "Confirm a meaningful message or placeholder is displayed instead of transcript."}]}]