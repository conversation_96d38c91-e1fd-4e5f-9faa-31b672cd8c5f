# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Data directories
data/
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# React build
frontend/build/
frontend/dist/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
#public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# AI model files (if downloaded locally)
*.pt
*.pth
*.bin
*.safetensors

# Video files (for testing)
*.mp4
*.avi
*.mov
*.mkv
*.webm
*.flv
*.wmv

# Backup files
*.bak
*.backup

# Test files and artifacts
test_*.js
test_*.py
test_*.sh
playwright-report/
test-results/
playwright.config.js

# Documentation files (optional - remove this line if you want to commit docs)
*_IMPLEMENTATION.md

testsprite_tests/*
testsprite_tests/